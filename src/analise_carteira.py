#!/usr/bin/env python3
"""
Script para análise de carteira de ações
Lê dados de um arquivo CSV e gera análises de rendimento individual e do conjunto
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import yfinance as yf
from datetime import datetime, timedelta
import os

# Configuração de estilo
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    try:
        carteira = pd.read_csv(arquivo_csv)
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
        return carteira
    except Exception as e:
        print(f"Erro ao carregar carteira: {e}")
        return None

def obter_dados_historicos(tickers, data_inicio):
    """Obtém dados históricos das ações"""
    print(f"Obtendo dados históricos a partir de {data_inicio.strftime('%d/%m/%Y')}...")
    dados = {}

    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            # Obter dados recentes (últimos 30 dias para garantir que temos dados suficientes)
            hist = stock.history(period='1mo')
            if not hist.empty:
                # Filtrar apenas dados a partir da data de início
                hist_filtrado = hist[hist.index.date >= data_inicio.date()]
                if not hist_filtrado.empty:
                    dados[ticker] = hist_filtrado
                    print(f"✓ Dados obtidos para {ticker}: {len(hist_filtrado)} dias desde {data_inicio.strftime('%d/%m/%Y')}")
                else:
                    print(f"✗ Nenhum dado encontrado para {ticker} desde {data_inicio.strftime('%d/%m/%Y')}")
            else:
                print(f"✗ Nenhum dado encontrado para {ticker}")
        except Exception as e:
            print(f"✗ Erro ao obter dados para {ticker}: {e}")

    return dados

def calcular_rendimento_individual(carteira, dados_historicos):
    """Calcula rendimento individual de cada ação"""
    resultados = []
    
    for _, posicao in carteira.iterrows():
        ticker = posicao['ticker']
        quantidade = posicao['quantidade']
        data_compra = posicao['data_compra']
        preco_compra = posicao.get('preco_compra', None)
        
        if ticker in dados_historicos:
            hist = dados_historicos[ticker]
            
            # Se não temos preço de compra, usar o preço do dia da compra
            if pd.isna(preco_compra) or preco_compra == 0:
                try:
                    preco_compra = hist.loc[hist.index.date >= data_compra.date(), 'Close'].iloc[0]
                except:
                    preco_compra = hist['Close'].iloc[0]
            
            preco_atual = hist['Close'].iloc[-1]
            
            # Cálculos
            valor_investido = quantidade * preco_compra
            valor_atual = quantidade * preco_atual
            rendimento_absoluto = valor_atual - valor_investido
            rendimento_percentual = (rendimento_absoluto / valor_investido) * 100
            
            # Dados históricos desde a compra (incluindo o dia da compra)
            dados_desde_compra = hist[hist.index.date >= data_compra.date()].copy()
            if dados_desde_compra.empty:
                # Se não há dados desde a compra, usar os dados disponíveis
                dados_desde_compra = hist.copy()
            dados_desde_compra['Valor_Posicao'] = dados_desde_compra['Close'] * quantidade
            
            resultado = {
                'ticker': ticker,
                'quantidade': quantidade,
                'data_compra': data_compra,
                'preco_compra': preco_compra,
                'preco_atual': preco_atual,
                'valor_investido': valor_investido,
                'valor_atual': valor_atual,
                'rendimento_absoluto': rendimento_absoluto,
                'rendimento_percentual': rendimento_percentual,
                'dados_historicos': dados_desde_compra
            }
            
            resultados.append(resultado)
    
    return resultados

def calcular_rendimento_carteira(resultados):
    """Calcula rendimento total da carteira"""
    valor_total_investido = sum(r['valor_investido'] for r in resultados)
    valor_total_atual = sum(r['valor_atual'] for r in resultados)
    rendimento_total_absoluto = valor_total_atual - valor_total_investido
    rendimento_total_percentual = (rendimento_total_absoluto / valor_total_investido) * 100 if valor_total_investido > 0 else 0
    
    # Criar série temporal da carteira
    todas_datas = set()
    for resultado in resultados:
        todas_datas.update(resultado['dados_historicos'].index.date)
    
    todas_datas = sorted(todas_datas)
    valor_carteira_diario = []
    
    for data in todas_datas:
        valor_dia = 0
        for resultado in resultados:
            hist = resultado['dados_historicos']
            try:
                preco_dia = hist[hist.index.date <= data]['Close'].iloc[-1]
                valor_dia += preco_dia * resultado['quantidade']
            except:
                continue
        valor_carteira_diario.append(valor_dia)
    
    carteira_historico = pd.DataFrame({
        'Data': todas_datas,
        'Valor_Total': valor_carteira_diario
    })
    carteira_historico['Data'] = pd.to_datetime(carteira_historico['Data'])
    carteira_historico.set_index('Data', inplace=True)
    
    return {
        'valor_total_investido': valor_total_investido,
        'valor_total_atual': valor_total_atual,
        'rendimento_total_absoluto': rendimento_total_absoluto,
        'rendimento_total_percentual': rendimento_total_percentual,
        'historico': carteira_historico
    }

def gerar_graficos(resultados, carteira_total):
    """Gera gráficos de análise"""
    os.makedirs('results/figures', exist_ok=True)

    # 1. Gráfico individual de cada ação (melhorado com mais informações)
    n_acoes = len(resultados)
    if n_acoes > 0:
        fig, axes = plt.subplots(n_acoes, 2, figsize=(16, 6*n_acoes))
        if n_acoes == 1:
            axes = axes.reshape(1, -1)

        for i, resultado in enumerate(resultados):
            ticker = resultado['ticker']
            ticker_clean = ticker.replace('.SA', '')
            hist = resultado['dados_historicos']

            # Calcular estatísticas
            performance = resultado['rendimento_percentual']
            preco_max = hist['Close'].max()
            preco_min = hist['Close'].min()

            # Gráfico de preço com área de preenchimento
            axes[i, 0].plot(hist.index, hist['Close'], label=f'{ticker_clean} - Preço',
                           linewidth=2.5, color='#1f77b4')
            axes[i, 0].fill_between(hist.index, hist['Low'], hist['High'],
                                   alpha=0.2, color='#ff7f0e', label='Faixa Diária (Min-Max)')
            axes[i, 0].axhline(y=resultado['preco_compra'], color='red', linestyle='--',
                              linewidth=2, label=f'Preço Compra: R$ {resultado["preco_compra"]:.2f}')

            # Título com performance
            cor_performance = 'green' if performance >= 0 else 'red'
            axes[i, 0].set_title(f'{ticker_clean} - Evolução do Preço (Performance: {performance:+.2f}%)',
                                fontsize=14, fontweight='bold', color=cor_performance)
            axes[i, 0].set_ylabel('Preço (R$)', fontsize=12)

            # Adicionar estatísticas no gráfico
            stats_text = f'Máximo: R$ {preco_max:.2f}\nMínimo: R$ {preco_min:.2f}\nAtual: R$ {resultado["preco_atual"]:.2f}'
            cor_box = 'lightgreen' if performance >= 0 else 'lightcoral'
            axes[i, 0].text(0.02, 0.98, stats_text, transform=axes[i, 0].transAxes,
                           fontsize=10, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor=cor_box, alpha=0.8))

            # Formatar eixo X - ajustar baseado no número de dias
            num_dias = len(hist)
            if num_dias == 1:
                # Para um único dia, usar formatação simples
                axes[i, 0].set_xticks([hist.index[0]])
                axes[i, 0].set_xticklabels([hist.index[0].strftime('%d/%m/%Y')])
            elif num_dias <= 7:
                axes[i, 0].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 0].xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(axes[i, 0].xaxis.get_majorticklabels(), rotation=45)
            else:
                axes[i, 0].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 0].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias//7)))
                plt.setp(axes[i, 0].xaxis.get_majorticklabels(), rotation=45)
            axes[i, 0].legend(loc='upper right')
            axes[i, 0].grid(True, alpha=0.3)

            # Gráfico de valor da posição com rendimento
            axes[i, 1].plot(hist.index, hist['Valor_Posicao'], label=f'{ticker_clean} - Valor Posição',
                           color='green', linewidth=2.5)
            axes[i, 1].axhline(y=resultado['valor_investido'], color='red', linestyle='--',
                              linewidth=2, label=f'Valor Investido: R$ {resultado["valor_investido"]:.2f}')

            # Área de lucro/prejuízo
            axes[i, 1].fill_between(hist.index, hist['Valor_Posicao'], resultado['valor_investido'],
                                   where=(hist['Valor_Posicao'] >= resultado['valor_investido']),
                                   color='green', alpha=0.3, label='Lucro')
            axes[i, 1].fill_between(hist.index, hist['Valor_Posicao'], resultado['valor_investido'],
                                   where=(hist['Valor_Posicao'] < resultado['valor_investido']),
                                   color='red', alpha=0.3, label='Prejuízo')

            axes[i, 1].set_title(f'{ticker_clean} - Valor da Posição (R$ {resultado["rendimento_absoluto"]:+.2f})',
                                fontsize=14, fontweight='bold', color=cor_performance)
            axes[i, 1].set_ylabel('Valor (R$)', fontsize=12)

            # Formatar eixo X - usar a mesma lógica do primeiro gráfico
            if num_dias == 1:
                # Para um único dia, usar formatação simples
                axes[i, 1].set_xticks([hist.index[0]])
                axes[i, 1].set_xticklabels([hist.index[0].strftime('%d/%m/%Y')])
            elif num_dias <= 7:
                axes[i, 1].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 1].xaxis.set_major_locator(mdates.DayLocator(interval=1))
                plt.setp(axes[i, 1].xaxis.get_majorticklabels(), rotation=45)
            else:
                axes[i, 1].xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
                axes[i, 1].xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias//7)))
                plt.setp(axes[i, 1].xaxis.get_majorticklabels(), rotation=45)
            axes[i, 1].legend(loc='upper left')
            axes[i, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('results/figures/rendimento_individual.png', dpi=300, bbox_inches='tight')
        plt.close()

    # 2. Gráfico da carteira total (melhorado com mais visualizações)
    if not carteira_total['historico'].empty:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Gráfico 1: Valor total da carteira
        ax1.plot(carteira_total['historico'].index, carteira_total['historico']['Valor_Total'],
                label='Valor Total da Carteira', linewidth=3, color='#1f77b4')
        ax1.axhline(y=carteira_total['valor_total_investido'], color='red', linestyle='--',
                   linewidth=2, label=f'Valor Investido: R$ {carteira_total["valor_total_investido"]:.2f}')

        # Área de lucro/prejuízo
        ax1.fill_between(carteira_total['historico'].index,
                        carteira_total['historico']['Valor_Total'],
                        carteira_total['valor_total_investido'],
                        where=(carteira_total['historico']['Valor_Total'] >= carteira_total['valor_total_investido']),
                        color='green', alpha=0.3, label='Lucro')
        ax1.fill_between(carteira_total['historico'].index,
                        carteira_total['historico']['Valor_Total'],
                        carteira_total['valor_total_investido'],
                        where=(carteira_total['historico']['Valor_Total'] < carteira_total['valor_total_investido']),
                        color='red', alpha=0.3, label='Prejuízo')

        performance_total = carteira_total['rendimento_total_percentual']
        cor_titulo = 'green' if performance_total >= 0 else 'red'
        ax1.set_title(f'Evolução do Valor Total da Carteira (Performance: {performance_total:+.2f}%)',
                     fontsize=14, fontweight='bold', color=cor_titulo)
        ax1.set_ylabel('Valor (R$)', fontsize=12)

        # Formatar eixo X baseado no número de dias
        num_dias_carteira = len(carteira_total['historico'])
        if num_dias_carteira <= 7:
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            ax1.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        else:
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            ax1.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias_carteira//7)))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Gráfico 2: Rendimento percentual acumulado
        rendimento_pct = ((carteira_total['historico']['Valor_Total'] - carteira_total['valor_total_investido']) /
                         carteira_total['valor_total_investido']) * 100
        ax2.plot(carteira_total['historico'].index, rendimento_pct,
                label='Rendimento %', linewidth=3, color='green' if performance_total >= 0 else 'red')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=1)
        ax2.fill_between(carteira_total['historico'].index, rendimento_pct, 0,
                        where=(rendimento_pct >= 0), color='green', alpha=0.3)
        ax2.fill_between(carteira_total['historico'].index, rendimento_pct, 0,
                        where=(rendimento_pct < 0), color='red', alpha=0.3)
        ax2.set_title('Rendimento Percentual da Carteira', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Rendimento (%)', fontsize=12)

        # Usar a mesma formatação de data
        if num_dias_carteira <= 7:
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            ax2.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        else:
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            ax2.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, num_dias_carteira//7)))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Gráfico 3: Comparação Investido vs Atual (barras)
        categorias = ['Valor Investido', 'Valor Atual']
        valores_totais = [carteira_total['valor_total_investido'], carteira_total['valor_total_atual']]
        cores = ['lightblue', 'lightgreen' if carteira_total['valor_total_atual'] >= carteira_total['valor_total_investido'] else 'lightcoral']

        bars = ax3.bar(categorias, valores_totais, color=cores, alpha=0.8, edgecolor='black', linewidth=1)
        ax3.set_ylabel('Valor (R$)', fontsize=12)
        ax3.set_title('Comparação: Investido vs Atual', fontsize=14, fontweight='bold')
        ax3.grid(True, alpha=0.3, axis='y')

        # Adicionar valores nas barras
        for bar, valor in zip(bars, valores_totais):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + valor*0.01,
                    f'R$ {valor:.2f}', ha='center', va='bottom', fontweight='bold')

        # Adicionar diferença
        diferenca = carteira_total['rendimento_total_absoluto']
        if abs(diferenca) > 0.01:
            cor_diferenca = 'green' if diferenca > 0 else 'red'
            simbolo = '+' if diferenca > 0 else ''
            ax3.text(0.5, max(valores_totais) * 0.9,
                    f'Diferença: {simbolo}R$ {diferenca:.2f}\n({simbolo}{performance_total:.2f}%)',
                    ha='center', va='center', fontsize=12, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.5', facecolor=cor_diferenca, alpha=0.2))

        # Gráfico 4: Distribuição da carteira (pizza)
        valores_por_acao = [r['valor_atual'] for r in resultados]
        labels_acao = [r['ticker'].replace('.SA', '') for r in resultados]
        ax4.pie(valores_por_acao, labels=labels_acao, autopct='%1.1f%%', startangle=90)
        ax4.set_title('Distribuição da Carteira (Valor Atual)', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig('results/figures/carteira_total.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("📊 Gráficos salvos em 'results/figures/':")
        print("  - rendimento_individual.png (evolução de cada ação)")
        print("  - carteira_total.png (análise completa da carteira)")
        print("📈 Para visualizar os gráficos, abra os arquivos PNG na pasta results/figures/")

def gerar_relatorio(resultados, carteira_total):
    """Gera relatório resumo"""
    print("\n" + "="*70)
    print("📊 RELATÓRIO DE ANÁLISE DA CARTEIRA - SÉRIES TEMPORAIS")
    print("="*70)

    print(f"\n📅 DATA DA ANÁLISE: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
    print(f"📈 PERÍODO ANALISADO: Desde 26/06/2025 (início dos investimentos)")

    # Status da carteira
    performance_total = carteira_total['rendimento_total_percentual']
    if performance_total > 0:
        status = "📈 LUCRO"
        emoji_status = "🟢"
    elif performance_total < 0:
        status = "📉 PREJUÍZO"
        emoji_status = "🔴"
    else:
        status = "➡️ NEUTRO"
        emoji_status = "🟡"

    print(f"\n{emoji_status} STATUS GERAL: {status}")

    print(f"\n💰 RESUMO FINANCEIRO:")
    print(f"  Valor Total Investido: R$ {carteira_total['valor_total_investido']:.2f}")
    print(f"  Valor Atual da Carteira: R$ {carteira_total['valor_total_atual']:.2f}")
    print(f"  Rendimento Absoluto: R$ {carteira_total['rendimento_total_absoluto']:+.2f}")
    print(f"  Rendimento Percentual: {carteira_total['rendimento_total_percentual']:+.2f}%")

    # Calcular dias de investimento
    if not carteira_total['historico'].empty:
        dias_investimento = len(carteira_total['historico'])
        print(f"  Dias de Investimento: {dias_investimento}")
        if dias_investimento > 1:
            rendimento_diario_medio = carteira_total['rendimento_total_percentual'] / dias_investimento
            print(f"  Rendimento Médio Diário: {rendimento_diario_medio:+.3f}%")

    print(f"\n📋 DETALHAMENTO POR AÇÃO:")
    print("-" * 90)

    # Ordenar por rendimento percentual (melhor primeiro)
    resultados_ordenados = sorted(resultados, key=lambda x: x['rendimento_percentual'], reverse=True)

    for i, resultado in enumerate(resultados_ordenados, 1):
        ticker_clean = resultado['ticker'].replace('.SA', '')
        performance = resultado['rendimento_percentual']

        if performance > 0:
            emoji_acao = "📈🟢"
        elif performance < 0:
            emoji_acao = "📉🔴"
        else:
            emoji_acao = "➡️🟡"

        print(f"\n{emoji_acao} #{i} {ticker_clean}:")
        print(f"    Quantidade: {resultado['quantidade']} ações")
        print(f"    Data da Compra: {resultado['data_compra'].strftime('%d/%m/%Y')}")
        print(f"    Preço de Compra: R$ {resultado['preco_compra']:.2f}")
        print(f"    Preço Atual: R$ {resultado['preco_atual']:.2f}")
        print(f"    Valor Investido: R$ {resultado['valor_investido']:.2f}")
        print(f"    Valor Atual: R$ {resultado['valor_atual']:.2f}")
        print(f"    Rendimento: R$ {resultado['rendimento_absoluto']:+.2f} ({resultado['rendimento_percentual']:+.2f}%)")

        # Participação na carteira
        participacao = (resultado['valor_atual'] / carteira_total['valor_total_atual']) * 100
        print(f"    Participação na Carteira: {participacao:.1f}%")

        # Variação de preço
        variacao_preco = ((resultado['preco_atual'] / resultado['preco_compra']) - 1) * 100
        print(f"    Variação do Preço: {variacao_preco:+.2f}%")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'

    print("📊 Iniciando análise da carteira com séries temporais...")

    # Carregar carteira
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None:
        return

    print(f"📈 Carteira carregada: {len(carteira)} posições")

    # Forçar data de início para 26/06/2025 conforme solicitado
    data_inicio = pd.to_datetime('2025-06-26')
    print(f"📅 Analisando séries temporais desde: {data_inicio.strftime('%d/%m/%Y')}")

    # Obter dados históricos - começar a partir de 26/06/2025
    tickers = carteira['ticker'].unique()
    dados_historicos = obter_dados_historicos(tickers, data_inicio)
    
    if not dados_historicos:
        print("Nenhum dado histórico foi obtido. Verifique os tickers.")
        return
    
    # Calcular rendimentos
    resultados = calcular_rendimento_individual(carteira, dados_historicos)
    carteira_total = calcular_rendimento_carteira(resultados)
    
    # Gerar gráficos
    gerar_graficos(resultados, carteira_total)
    
    # Gerar relatório
    gerar_relatorio(resultados, carteira_total)
    
    # Salvar resultados em CSV
    os.makedirs('results', exist_ok=True)
    df_resultados = pd.DataFrame([{
        'ticker': r['ticker'],
        'quantidade': r['quantidade'],
        'data_compra': r['data_compra'],
        'preco_compra': r['preco_compra'],
        'preco_atual': r['preco_atual'],
        'valor_investido': r['valor_investido'],
        'valor_atual': r['valor_atual'],
        'rendimento_absoluto': r['rendimento_absoluto'],
        'rendimento_percentual': r['rendimento_percentual']
    } for r in resultados])
    
    df_resultados.to_csv('results/analise_carteira.csv', index=False)
    print(f"\nResultados salvos em 'results/analise_carteira.csv'")

if __name__ == "__main__":
    main()
